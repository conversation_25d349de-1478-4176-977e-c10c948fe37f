"""
CrewAI-Enhanced FastAPI Backend for AWS Cost Optimization
Multi-agent orchestration with specialized AWS consulting capabilities
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import asyncio
import logging
from contextlib import asynccontextmanager
import uvicorn
import os
import uuid
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import CrewAI manager, session management, and MCP backend
from session_manager_new import session_manager
from crewai_aws_manager import get_crewai_manager
from mcp_backend import MCPClientManager, DEFAULT_MCP_SERVERS

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CrewAIChatResponse(BaseModel):
    response: str
    conversation_id: str
    crew_used: str
    agents_involved: List[str] = []
    tools_used: List[Dict[str, Any]] = []
    status: str = "success"
    session_stats: Optional[Dict[str, Any]] = None
    context_used: bool = False
    query_type: Optional[str] = None

class ChatRequest(BaseModel):
    message: str
    conversation_id: Optional[str] = None
    crew_type: Optional[str] = None  # Allow specific crew selection
    use_tools: bool = True

# Background workers
async def session_monitor_worker():
    """Background worker to monitor session health"""
    try:
        while True:
            try:
                stats = session_manager.get_all_sessions_stats()
                active_count = stats.get("active_sessions", 0)
                if active_count > 0:
                    logger.info(f"Active sessions: {active_count}")
                await asyncio.sleep(300)
            except asyncio.CancelledError:
                logger.info("Session monitor worker cancelled")
                break
            except Exception as e:
                logger.error(f"Session monitor error: {e}")
                await asyncio.sleep(60)
    except asyncio.CancelledError:
        logger.info("Session monitor worker cancelled")
    except Exception as e:
        logger.error(f"Session monitor worker error: {e}")

@asynccontextmanager
async def enhanced_lifespan(app: FastAPI):
    """Enhanced lifespan with CrewAI initialization"""
    logger.info("Starting CrewAI-Enhanced AWS Cost Optimization API")
    
    # Validate session management
    try:
        test_stats = session_manager.get_all_sessions_stats()
        if not session_manager.backend:
            logger.warning("Bedrock backend not available, using local-only mode")
        else:
            logger.info("✅ Session management validated successfully")
    except Exception as e:
        logger.error(f"Session management validation failed: {e}")
    
    # Initialize CrewAI agents
    try:
        crewai_manager = get_crewai_manager()
        logger.info("✅ CrewAI agents initialized successfully")
        logger.info(f"Available crews: {list(crewai_manager.crews.keys())}")
        logger.info(f"Available agents: {list(crewai_manager.agents.keys())}")
    except Exception as e:
        logger.error(f"CrewAI initialization failed: {e}")
        raise
    
    monitor_task = asyncio.create_task(session_monitor_worker())
    
    try:
        yield
    finally:
        logger.info("Shutting down CrewAI-Enhanced AWS API")
        
        monitor_task.cancel()
        try:
            await monitor_task
        except asyncio.CancelledError:
            pass

app = FastAPI(
    title="CrewAI-Enhanced AWS Cost Optimization & Architecture Design API",
    description="Multi-agent AI system for specialized AWS consulting and cost optimization",
    version="3.0.0",
    lifespan=enhanced_lifespan
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Health check endpoint with CrewAI capabilities"""
    session_stats = session_manager.get_all_sessions_stats()
    
    return {
        "message": "CrewAI-Enhanced AWS Cost Optimization & Architecture Design API is running",
        "status": "healthy",
        "mode": "crewai_multi_agent",
        "version": "3.0.0",
        "features": [
            "multi_agent_coordination",
            "specialized_aws_crews",
            "intelligent_task_delegation",
            "context_retention",
            "bedrock_persistence"
        ],
        "available_crews": list(get_crewai_manager().crews.keys()),
        "available_agents": [agent.role for agent in get_crewai_manager().agents.values()],
        "session_stats": session_stats
    }

@app.post("/chat", response_model=CrewAIChatResponse)
async def crewai_chat_endpoint(request: ChatRequest):
    """CrewAI-enhanced chat endpoint with multi-agent coordination"""
    try:
        conversation_id = request.conversation_id or f"crew_{uuid.uuid4().hex[:8]}"
        
        # Circuit breaker check
        crewai_manager = get_crewai_manager()
        if len(crewai_manager._recent_model_errors) >= 3:
            logger.warning("Circuit breaker activated: using simplified mode")

        # Execute CrewAI analysis
        result = await crewai_manager.chat_with_crewai_context(
            message=request.message,
            session_id=conversation_id,
            crew_type=request.crew_type
        )
        
        # Get session stats
        chat_session = session_manager.get_session(conversation_id)
        session_stats = chat_session.get_session_stats() if chat_session else {"error": "Session not found"}
        
        return CrewAIChatResponse(
            response=result["response"],
            conversation_id=conversation_id,
            crew_used=result["crew_used"],
            agents_involved=result.get("agents_involved", []),
            tools_used=result["tools_used"],
            status="success" if not result.get("error") else "partial",
            session_stats=session_stats,
            context_used=session_stats.get('total_turns', 0) > 0 if session_stats and 'error' not in session_stats else False,
            query_type=result["crew_used"]
        )
        
    except Exception as e:
        logger.error(f"CrewAI chat error: {e}")
        
        # Graceful degradation
        return CrewAIChatResponse(
            response="I'm experiencing a temporary processing issue. Let me provide a simplified AWS consultation based on your question.",
            conversation_id=conversation_id,
            crew_used="fallback",
            agents_involved=["fallback_agent"],
            tools_used=[],
            status="degraded",
            query_type="fallback"
        )

# Specialized CrewAI endpoints
@app.post("/analyze/cost-optimization")
async def analyze_cost_optimization(request: dict):
    """Specialized endpoint for cost optimization using cost analysis crew"""
    try:
        prompt = f"""
Perform comprehensive cost analysis for my AWS environment:

Current services: {', '.join(request.get('current_services', []))}
Monthly budget: ${request.get('monthly_budget', 'not specified')}
Growth rate: {request.get('growth_rate', 'not specified')}
Region: {request.get('region', 'ap-south-1')}

Please provide:
1. Detailed cost breakdown and trends
2. Specific optimization opportunities with savings estimates
3. Implementation roadmap with priorities
4. ROI analysis for optimization investments
"""
        
        chat_request = ChatRequest(message=prompt, crew_type="cost_analysis", use_tools=True)
        return await crewai_chat_endpoint(chat_request)
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/design/architecture-solution")
async def design_architecture_solution(request: dict):
    """Specialized endpoint for architecture design using architecture crew"""
    try:
        prompt = f"""
Design a comprehensive AWS solution architecture:

Application type: {request.get('app_type', 'web application')}
Expected users: {request.get('expected_users', 10000)}
Performance requirements: {request.get('performance_requirements', 'standard')}
Availability target: {request.get('availability_target', '99.9%')}
Compliance needs: {', '.join(request.get('compliance', []))}
Regions: {', '.join(request.get('regions', ['ap-south-1']))}
Budget constraints: ${request.get('budget_limit', 'flexible')}

Please provide:
1. Complete architecture design with diagrams
2. Detailed component specifications
3. Cost breakdown (setup + operational)
4. Security and compliance implementation
5. Scalability and disaster recovery strategy
6. Implementation timeline and milestones
"""
        
        chat_request = ChatRequest(message=prompt, crew_type="architecture_design", use_tools=True)
        return await crewai_chat_endpoint(chat_request)
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/optimize/current-setup")
async def optimize_current_setup(request: dict):
    """Specialized endpoint for optimizing existing AWS setups"""
    try:
        prompt = f"""
Optimize my current AWS setup:

Current architecture: {request.get('current_architecture', 'not specified')}
Performance issues: {', '.join(request.get('performance_issues', []))}
Cost concerns: {request.get('cost_concerns', 'high monthly bills')}
Workload patterns: {request.get('workload_patterns', 'variable')}
Business constraints: {request.get('business_constraints', 'none')}

Please provide:
1. Comprehensive analysis of current setup
2. Specific optimization recommendations
3. Expected performance improvements
4. Cost savings estimates with timeline
5. Risk assessment and mitigation strategies
6. Implementation plan with priorities
"""
        
        chat_request = ChatRequest(message=prompt, crew_type="optimization_review", use_tools=True)
        return await crewai_chat_endpoint(chat_request)
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# CrewAI Management endpoints
@app.get("/crews")
async def list_crews():
    """List all available CrewAI crews and their capabilities"""
    crews_info = {}
    crewai_manager = get_crewai_manager()

    for crew_name, crew in crewai_manager.crews.items():
        crews_info[crew_name] = {
            "name": crew_name,
            "agents": [agent.role for agent in crew.agents],
            "process_type": crew.process.value if hasattr(crew.process, 'value') else str(crew.process),
            "capabilities": {
                "cost_analysis": crew_name in ["cost_analysis", "comprehensive_analysis"],
                "architecture_design": crew_name in ["architecture_design", "comprehensive_analysis"],
                "optimization": crew_name in ["optimization_review", "comprehensive_analysis"],
                "compliance": crew_name in ["architecture_design", "comprehensive_analysis"]
            }
        }
    
    return crews_info

@app.get("/agents")
async def list_agents():
    """List all available CrewAI agents and their specializations"""
    agents_info = {}
    crewai_manager = get_crewai_manager()

    for agent_name, agent in crewai_manager.agents.items():
        agents_info[agent_name] = {
            "role": agent.role,
            "goal": agent.goal,
            "backstory": agent.backstory[:200] + "..." if len(agent.backstory) > 200 else agent.backstory,
            "tools_count": len(agent.tools) if hasattr(agent, 'tools') else 0,
            "capabilities": {
                "delegation": agent.allow_delegation if hasattr(agent, 'allow_delegation') else False,
                "memory": agent.memory if hasattr(agent, 'memory') else False,
                "max_iterations": agent.max_iter if hasattr(agent, 'max_iter') else 0
            }
        }
    
    return agents_info

# Session management endpoints (inherited from previous version)
@app.get("/sessions/{session_id}/history")
async def get_session_history(session_id: str):
    """Get conversation history for a session"""
    chat_session = session_manager.get_session(session_id)
    if not chat_session:
        raise HTTPException(status_code=404, detail="Session not found")
    
    session_stats = chat_session.get_session_stats()
    history = [turn.to_dict() for turn in chat_session.conversation_history]
    
    return {
        "session_id": session_id,
        "created_at": session_stats.get('created_at'),
        "last_activity": session_stats.get('last_activity'),
        "message_count": len(history),
        "total_tools_used": session_stats.get('total_tools_used', 0),
        "history": history
    }

@app.get("/sessions")
async def list_sessions():
    """List all active sessions with basic stats"""
    sessions_info = {}
    for sid, chat_session in session_manager.sessions.items():
        try:
            session_stats = chat_session.get_session_stats()
            sessions_info[sid] = session_stats
        except Exception as e:
            logger.warning(f"Could not get stats for session {sid}: {e}")
    
    return {
        "sessions": sessions_info,
        "total_sessions": len(sessions_info),
        "global_stats": session_manager.get_all_sessions_stats()
    }

@app.delete("/sessions/{session_id}")
async def clear_session(session_id: str):
    """Delete a session"""
    try:
        session_manager.delete_session(session_id)
        return {"message": f"Session {session_id} deleted successfully"}
    except Exception as e:
        logger.error(f"Error deleting session: {e}")
        raise HTTPException(status_code=404, detail="Session not found or could not be deleted")

if __name__ == "__main__":
    uvicorn.run(
        "main_enhanced:app",
        host=os.getenv("API_HOST", "0.0.0.0"),
        port=int(os.getenv("API_PORT", "8000")),
        reload=True,
        log_level=os.getenv("LOG_LEVEL", "info")
    )
