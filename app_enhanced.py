"""
CrewAI-Enhanced Streamlit Frontend for AWS Cost Optimization
Multi-agent interface with specialized crew selection
"""

import streamlit as st
import requests
import json
import uuid
from datetime import datetime
import pandas as pd
from typing import Optional, Dict, List, Any
import plotly.express as px
import plotly.graph_objects as go
from urllib.parse import urljoin
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")
API_TIMEOUT = int(os.getenv("API_TIMEOUT", "120"))

# Page configuration
st.set_page_config(
    page_title="CrewAI AWS Cost Optimization Assistant",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Enhanced CSS for CrewAI interface
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #FF6B6B, #4ECDC4, #45B7D1);
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 2rem;
    }
    .crew-card {
        background: white;
        padding: 1rem;
        border-radius: 0.5rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin: 0.5rem 0;
        border-left: 4px solid #4ECDC4;
    }
    .agent-card {
        background: #f8f9fa;
        padding: 0.8rem;
        border-radius: 0.3rem;
        margin: 0.3rem 0;
        border-left: 3px solid #FF6B6B;
    }
    .chat-message {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
        border-left: 4px solid #45B7D1;
    }
    .success-message {
        background: #d4edda;
        color: #155724;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
        border-left: 4px solid #28a745;
    }
    .crew-status {
        display: inline-block;
        padding: 0.2rem 0.6rem;
        border-radius: 1rem;
        font-size: 0.8rem;
        font-weight: bold;
        margin: 0.2rem;
    }
    .crew-active {
        background-color: #28a745;
        color: white;
    }
    .crew-available {
        background-color: #17a2b8;
        color: white;
    }
</style>
""", unsafe_allow_html=True)

class CrewAIAPIClient:
    def __init__(self, base_url: str, timeout: int = 120):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """Make HTTP request with enhanced error handling"""
        url = urljoin(self.base_url + '/', endpoint.lstrip('/'))
        
        try:
            response = requests.request(method, url, timeout=self.timeout, **kwargs)
            response.raise_for_status()
            return response
        
        except requests.exceptions.Timeout:
            st.error(f"Request timed out after {self.timeout} seconds")
            raise
        except requests.exceptions.ConnectionError:
            st.error(f"Unable to connect to the API server at {self.base_url}")
            raise
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 503:
                st.error("CrewAI service is initializing. Please wait a moment and try again.")
            else:
                st.error(f"API Error ({e.response.status_code}): {e.response.text}")
            raise
        except Exception as e:
            st.error(f"Unexpected error: {str(e)}")
            raise
    
    def chat_with_crewai(self, message: str, session_id: str, crew_type: Optional[str] = None) -> Dict:
        """Send chat message to CrewAI system"""
        payload = {
            "message": message,
            "conversation_id": session_id,
            "crew_type": crew_type,
            "use_tools": True
        }
        response = self._make_request("POST", "/chat", json=payload)
        return response.json()
    
    def get_crews_info(self) -> Dict:
        """Get information about available crews"""
        response = self._make_request("GET", "/crews")
        return response.json()
    
    def get_agents_info(self) -> Dict:
        """Get information about available agents"""
        response = self._make_request("GET", "/agents")
        return response.json()
    
    def analyze_cost_optimization(self, request_data: Dict) -> Dict:
        """Specialized cost optimization analysis"""
        response = self._make_request("POST", "/analyze/cost-optimization", json=request_data)
        return response.json()
    
    def design_architecture_solution(self, request_data: Dict) -> Dict:
        """Specialized architecture design"""
        response = self._make_request("POST", "/design/architecture-solution", json=request_data)
        return response.json()
    
    def optimize_current_setup(self, request_data: Dict) -> Dict:
        """Specialized current setup optimization"""
        response = self._make_request("POST", "/optimize/current-setup", json=request_data)
        return response.json()

# Initialize API client
@st.cache_resource
def get_api_client():
    return CrewAIAPIClient(API_BASE_URL, API_TIMEOUT)

api_client = get_api_client()

# Initialize session state
def initialize_session_state():
    if 'session_id' not in st.session_state:
        st.session_state.session_id = f"crewai_{uuid.uuid4().hex[:12]}"
    if 'conversation_history' not in st.session_state:
        st.session_state.conversation_history = []
    if 'selected_crew' not in st.session_state:
        st.session_state.selected_crew = None

initialize_session_state()

# Header
st.markdown("""
<div class="main-header">
    <h1 style="color: white; margin: 0;">🤖 CrewAI AWS Cost Optimization Assistant</h1>
    <p style="color: white; margin: 0; opacity: 0.9;">Multi-agent AI system for specialized AWS consulting and architecture design</p>
</div>
""", unsafe_allow_html=True)

# Sidebar - CrewAI Status and Selection
with st.sidebar:
    st.header("🤖 CrewAI System")
    
    try:
        # Get system status
        response = requests.get(f"{API_BASE_URL}/", timeout=5)
        if response.status_code == 200:
            system_info = response.json()
            
            st.markdown("### System Status")
            st.success(f"✅ {system_info['message']}")
            st.info(f"Version: {system_info.get('version', 'Unknown')}")
            
            # Display available crews
            st.markdown("### Available Crews")
            crews_info = api_client.get_crews_info()
            
            for crew_name, crew_data in crews_info.items():
                with st.expander(f"🎯 {crew_name.replace('_', ' ').title()}"):
                    st.write(f"**Agents:** {len(crew_data['agents'])}")
                    for agent in crew_data['agents']:
                        st.write(f"• {agent}")
                    
                    st.write(f"**Process:** {crew_data['process_type']}")
                    
                    capabilities = crew_data['capabilities']
                    cap_list = [cap for cap, enabled in capabilities.items() if enabled]
                    if cap_list:
                        st.write(f"**Capabilities:** {', '.join(cap_list)}")
            
            # Crew selection
            st.markdown("### Crew Selection")
            crew_options = ["Auto-detect"] + list(crews_info.keys())
            selected_crew = st.selectbox(
                "Choose crew for next query:",
                crew_options,
                format_func=lambda x: x.replace('_', ' ').title()
            )
            st.session_state.selected_crew = None if selected_crew == "Auto-detect" else selected_crew
            
        else:
            st.error("❌ CrewAI system not responding")
    
    except Exception as e:
        st.error(f"❌ Failed to connect to CrewAI system: {e}")

# Main interface tabs
tab1, tab2, tab3, tab4, tab5 = st.tabs([
    "💰 Cost Optimization",
    "🏗️ Architecture Design", 
    "⚡ Setup Optimization",
    "💬 Multi-Agent Chat",
    "📊 CrewAI Analytics"
])

# Tab 1: Cost Optimization
with tab1:
    st.markdown("""
    <div class="crew-card">
        <h3>💰 Cost Optimization Analysis</h3>
        <p>Uses <strong>Cost Analysis Crew</strong>: Cost Analyst + Optimization Specialist</p>
    </div>
    """, unsafe_allow_html=True)
    
    with st.form("cost_optimization_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            current_services = st.multiselect(
                "Current AWS Services",
                ["EC2", "RDS", "S3", "Lambda", "DynamoDB", "CloudFront", "ELB", "VPC", "Route53", "EKS", "ECS"],
                default=["EC2", "RDS", "S3"]
            )
            
            monthly_budget = st.number_input(
                "Current Monthly Budget ($)",
                min_value=100,
                max_value=100000,
                value=5000,
                step=100
            )
        
        with col2:
            growth_rate = st.selectbox(
                "Expected Growth Rate",
                ["10% annually", "25% annually", "50% annually", "100% annually", "Variable/Seasonal"]
            )
            
            region = st.selectbox(
                "Primary AWS Region",
                ["ap-south-1", "us-east-1", "us-west-2", "eu-west-1", "ap-southeast-1"]
            )
        
        priority_areas = st.multiselect(
            "Optimization Priority Areas",
            ["Compute costs", "Storage costs", "Data transfer", "Database optimization", "Reserved instances", "Spot instances"],
            default=["Compute costs", "Reserved instances"]
        )
        
        submitted = st.form_submit_button("🤖 Analyze with Cost Crew", use_container_width=True)
        
        if submitted:
            with st.spinner("CrewAI Cost Analysis Crew is working on your optimization..."):
                try:
                    request_data = {
                        "current_services": current_services,
                        "monthly_budget": monthly_budget,
                        "growth_rate": growth_rate,
                        "region": region,
                        "priority_areas": priority_areas
                    }
                    
                    result = api_client.analyze_cost_optimization(request_data)
                    
                    st.markdown(f"""
                    <div class="success-message">
                        <strong>✅ Cost Analysis Complete</strong><br>
                        Crew Used: <span class="crew-status crew-active">{result.get('crew_used', 'cost_analysis')}</span><br>
                        Agents Involved: {', '.join(result.get('agents_involved', []))}<br>
                        Tools Executed: {len(result.get('tools_used', []))}
                    </div>
                    """, unsafe_allow_html=True)
                    
                    st.markdown("### 💰 Cost Optimization Report")
                    st.markdown(f"""
                    <div class="chat-message">
                        {result['response']}
                    </div>
                    """, unsafe_allow_html=True)
                    
                    # Show crew execution details
                    if result.get('agents_involved'):
                        with st.expander("🤖 CrewAI Execution Details"):
                            for agent in result['agents_involved']:
                                st.write(f"🤖 **{agent}** contributed to this analysis")
                            
                            if result.get('tools_used'):
                                st.write("**Tools Used:**")
                                for tool in result['tools_used']:
                                    st.write(f"• {tool.get('tool_name', 'Unknown tool')}")
                
                except Exception as e:
                    st.error(f"Cost optimization analysis failed: {str(e)}")

# Tab 2: Architecture Design
with tab2:
    st.markdown("""
    <div class="crew-card">
        <h3>🏗️ Architecture Solution Design</h3>
        <p>Uses <strong>Architecture Design Crew</strong>: Solutions Architect + Cost Analyst + Compliance Advisor</p>
    </div>
    """, unsafe_allow_html=True)
    
    with st.form("architecture_design_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            app_type = st.selectbox(
                "Application Type",
                ["Web Application", "Mobile Backend", "Data Analytics", "Machine Learning", "IoT Platform", "E-commerce", "Enterprise SaaS"]
            )
            
            expected_users = st.selectbox(
                "Expected Users",
                ["< 1,000", "1,000 - 10,000", "10,000 - 100,000", "100,000 - 1M", "> 1M"]
            )
            
            performance_requirements = st.selectbox(
                "Performance Requirements",
                ["Standard", "High Performance", "Ultra Low Latency", "High Throughput"]
            )
        
        with col2:
            availability_target = st.selectbox(
                "Availability Target",
                ["99.9% (8.77h downtime/year)", "99.95% (4.38h downtime/year)", "99.99% (52.6min downtime/year)", "99.999% (5.26min downtime/year)"]
            )
            
            regions = st.multiselect(
                "Deployment Regions",
                ["ap-south-1", "us-east-1", "us-west-2", "eu-west-1", "ap-southeast-1", "ap-northeast-1"],
                default=["ap-south-1"]
            )
            
            budget_limit = st.number_input(
                "Monthly Budget Limit ($)",
                min_value=500,
                max_value=50000,
                value=5000,
                step=500
            )
        
        compliance = st.multiselect(
            "Compliance Requirements",
            ["SOC 2", "ISO 27001", "PCI DSS", "HIPAA", "GDPR", "FedRAMP", "None"]
        )
        
        special_requirements = st.text_area(
            "Special Requirements",
            placeholder="e.g., Real-time processing, Machine learning inference, High security, Disaster recovery..."
        )
        
        submitted = st.form_submit_button("🤖 Design with Architecture Crew", use_container_width=True)
        
        if submitted:
            with st.spinner("CrewAI Architecture Design Crew is creating your solution..."):
                try:
                    request_data = {
                        "app_type": app_type,
                        "expected_users": expected_users,
                        "performance_requirements": performance_requirements,
                        "availability_target": availability_target,
                        "regions": regions,
                        "budget_limit": budget_limit,
                        "compliance": compliance,
                        "special_requirements": special_requirements
                    }
                    
                    result = api_client.design_architecture_solution(request_data)
                    
                    st.markdown(f"""
                    <div class="success-message">
                        <strong>✅ Architecture Design Complete</strong><br>
                        Crew Used: <span class="crew-status crew-active">{result.get('crew_used', 'architecture_design')}</span><br>
                        Agents Involved: {', '.join(result.get('agents_involved', []))}<br>
                        Collaborative Analysis: Multi-agent validation
                    </div>
                    """, unsafe_allow_html=True)
                    
                    st.markdown("### 🏗️ Complete Architecture Solution")
                    st.markdown(f"""
                    <div class="chat-message">
                        {result['response']}
                    </div>
                    """, unsafe_allow_html=True)
                    
                    # Show multi-agent collaboration
                    if result.get('agents_involved'):
                        with st.expander("🤖 Multi-Agent Collaboration Details"):
                            agent_roles = {
                                "AWS Solutions Architect": "🏗️ Architecture design and component selection",
                                "Senior AWS Cost Analyst": "💰 Cost estimation and optimization",
                                "AWS Compliance and Security Advisor": "🔒 Security and compliance validation"
                            }
                            
                            for agent in result['agents_involved']:
                                if agent in agent_roles:
                                    st.write(f"{agent_roles[agent]}")
                                else:
                                    st.write(f"🤖 **{agent}** contributed to this design")
                
                except Exception as e:
                    st.error(f"Architecture design failed: {str(e)}")

# Tab 3: Setup Optimization
with tab3:
    st.markdown("""
    <div class="crew-card">
        <h3>⚡ Current Setup Optimization</h3>
        <p>Uses <strong>Optimization Review Crew</strong>: Optimization Specialist + Cost Analyst</p>
    </div>
    """, unsafe_allow_html=True)
    
    with st.form("setup_optimization_form"):
        current_architecture = st.text_area(
            "Current Architecture Description",
            placeholder="Describe your current AWS setup: EC2 instances, databases, storage, networking, etc.",
            height=100
        )
        
        col1, col2 = st.columns(2)
        
        with col1:
            performance_issues = st.multiselect(
                "Performance Issues",
                ["Slow response times", "High latency", "Database bottlenecks", "Storage I/O issues", "Network congestion", "Memory constraints"]
            )
            
            cost_concerns = st.text_input(
                "Cost Concerns",
                value="High monthly bills"
            )
        
        with col2:
            workload_patterns = st.selectbox(
                "Workload Patterns",
                ["Steady/Predictable", "Variable/Seasonal", "Spike/Burst", "Development/Testing", "Mixed patterns"]
            )
            
            business_constraints = st.text_area(
                "Business Constraints",
                placeholder="e.g., Cannot change during business hours, Limited budget, Compliance requirements..."
            )
        
        submitted = st.form_submit_button("🤖 Optimize with Review Crew", use_container_width=True)
        
        if submitted and current_architecture:
            with st.spinner("CrewAI Optimization Review Crew is analyzing your setup..."):
                try:
                    request_data = {
                        "current_architecture": current_architecture,
                        "performance_issues": performance_issues,
                        "cost_concerns": cost_concerns,
                        "workload_patterns": workload_patterns,
                        "business_constraints": business_constraints
                    }
                    
                    result = api_client.optimize_current_setup(request_data)
                    
                    st.markdown(f"""
                    <div class="success-message">
                        <strong>✅ Optimization Analysis Complete</strong><br>
                        Crew Used: <span class="crew-status crew-active">{result.get('crew_used', 'optimization_review')}</span><br>
                        Agents Involved: {', '.join(result.get('agents_involved', []))}<br>
                        Focused Analysis: Performance + Cost optimization
                    </div>
                    """, unsafe_allow_html=True)
                    
                    st.markdown("### ⚡ Optimization Recommendations")
                    st.markdown(f"""
                    <div class="chat-message">
                        {result['response']}
                    </div>
                    """, unsafe_allow_html=True)
                
                except Exception as e:
                    st.error(f"Setup optimization failed: {str(e)}")

# Tab 4: Multi-Agent Chat
with tab4:
    st.markdown("""
    <div class="crew-card">
        <h3>💬 Multi-Agent AWS Consultation</h3>
        <p>Interactive chat with intelligent crew selection and agent coordination</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Chat interface
    chat_container = st.container()
    
    with chat_container:
        # Display conversation history
        if st.session_state.conversation_history:
            st.markdown("### 📝 Conversation History")
            for i, message in enumerate(st.session_state.conversation_history):
                if message["role"] == "user":
                    st.markdown(f"""
                    <div class="chat-message" style="background: #e3f2fd;">
                        <strong>🤔 You:</strong> {message["content"]}
                    </div>
                    """, unsafe_allow_html=True)
                else:
                    crew_used = message.get("crew_used", "unknown")
                    agents_involved = message.get("agents_involved", [])
                    
                    st.markdown(f"""
                    <div class="chat-message" style="background: #f3e5f5;">
                        <strong>🤖 CrewAI System:</strong> {message["content"]}<br>
                        <small><strong>Crew:</strong> <span class="crew-status crew-active">{crew_used}</span> | 
                        <strong>Agents:</strong> {', '.join(agents_involved[:2])}{'...' if len(agents_involved) > 2 else ''}</small>
                    </div>
                    """, unsafe_allow_html=True)
        
        # Chat input
        user_input = st.text_area(
            "Ask your AWS question:",
            placeholder="e.g., How can I reduce my EC2 costs while maintaining performance? Design a scalable architecture for my e-commerce platform.",
            height=100,
            key="chat_input"
        )
        
        col1, col2, col3 = st.columns([1, 1, 2])
        
        with col1:
            send_button = st.button("🤖 Send to CrewAI", use_container_width=True)
        
        with col2:
            clear_button = st.button("🗑️ Clear Chat", use_container_width=True)
        
        with col3:
            selected_crew_display = st.session_state.selected_crew or "Auto-detect"
            st.info(f"Selected Crew: {selected_crew_display.replace('_', ' ').title()}")
        
        if clear_button:
            st.session_state.conversation_history = []
            st.rerun()
        
        if send_button and user_input.strip():
            with st.spinner("CrewAI agents are collaborating on your request..."):
                try:
                    # Add user message to history
                    st.session_state.conversation_history.append({
                        "role": "user",
                        "content": user_input,
                        "timestamp": datetime.now().isoformat()
                    })
                    
                    # Send to CrewAI
                    result = api_client.chat_with_crewai(
                        message=user_input,
                        session_id=st.session_state.session_id,
                        crew_type=st.session_state.selected_crew
                    )
                    
                    # Add assistant response to history
                    st.session_state.conversation_history.append({
                        "role": "assistant",
                        "content": result["response"],
                        "timestamp": datetime.now().isoformat(),
                        "crew_used": result.get("crew_used", "unknown"),
                        "agents_involved": result.get("agents_involved", []),
                        "tools_used": result.get("tools_used", [])
                    })
                    
                    # Show success message
                    st.markdown(f"""
                    <div class="success-message">
                        <strong>✅ CrewAI Response Generated</strong><br>
                        Crew: <span class="crew-status crew-active">{result.get('crew_used', 'unknown')}</span><br>
                        Agents: {', '.join(result.get('agents_involved', []))}<br>
                        Status: {result.get('status', 'success')}
                    </div>
                    """, unsafe_allow_html=True)
                    
                    # Clear input and refresh
                    st.rerun()
                    
                except Exception as e:
                    st.error(f"CrewAI consultation failed: {str(e)}")

# Tab 5: CrewAI Analytics
with tab5:
    st.markdown("""
    <div class="crew-card">
        <h3>📊 CrewAI System Analytics</h3>
        <p>Monitor multi-agent performance, crew utilization, and system health</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Refresh button
    if st.button("🔄 Refresh Analytics", use_container_width=True):
        st.rerun()
    
    try:
        # Get system information
        system_response = requests.get(f"{API_BASE_URL}/", timeout=10)
        if system_response.status_code == 200:
            system_info = system_response.json()
            
            # System Overview
            st.subheader("🤖 CrewAI System Overview")
            
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("System Version", system_info.get('version', 'Unknown'))
            
            with col2:
                available_crews = len(system_info.get('available_crews', []))
                st.metric("Available Crews", available_crews)
            
            with col3:
                available_agents = len(system_info.get('available_agents', []))
                st.metric("AI Agents", available_agents)
            
            with col4:
                features_count = len(system_info.get('features', []))
                st.metric("Features", features_count)
            
            # Crew Information
            st.subheader("🎯 Crew Capabilities")
            
            crews_info = api_client.get_crews_info()
            agents_info = api_client.get_agents_info()
            
            # Create crew overview DataFrame
            crew_data = []
            for crew_name, crew_info in crews_info.items():
                capabilities = crew_info['capabilities']
                active_capabilities = [cap for cap, enabled in capabilities.items() if enabled]
                
                crew_data.append({
                    "Crew": crew_name.replace('_', ' ').title(),
                    "Agents": len(crew_info['agents']),
                    "Process": crew_info['process_type'],
                    "Capabilities": ', '.join(active_capabilities)
                })
            
            df_crews = pd.DataFrame(crew_data)
            st.dataframe(df_crews, use_container_width=True)
            
            # Agent Specializations
            st.subheader("🤖 Agent Specializations")
            
            for agent_name, agent_info in agents_info.items():
                with st.expander(f"🤖 {agent_info['role']}"):
                    st.write(f"**Goal:** {agent_info['goal']}")
                    st.write(f"**Specialization:** {agent_info['backstory']}")
                    
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("Tools", agent_info['tools_count'])
                    with col2:
                        delegation = "✅" if agent_info['capabilities']['delegation'] else "❌"
                        st.write(f"**Delegation:** {delegation}")
                    with col3:
                        memory = "✅" if agent_info['capabilities']['memory'] else "❌"
                        st.write(f"**Memory:** {memory}")
            
            # Conversation Analytics
            st.subheader("📈 Conversation Analytics")
            
            if st.session_state.conversation_history:
                # Analyze crew usage
                crew_usage = {}
                agent_usage = {}
                
                for msg in st.session_state.conversation_history:
                    if msg["role"] == "assistant":
                        crew = msg.get("crew_used", "unknown")
                        crew_usage[crew] = crew_usage.get(crew, 0) + 1
                        
                        for agent in msg.get("agents_involved", []):
                            agent_usage[agent] = agent_usage.get(agent, 0) + 1
                
                if crew_usage:
                    # Crew usage chart
                    fig_crew = px.pie(
                        values=list(crew_usage.values()),
                        names=[name.replace('_', ' ').title() for name in crew_usage.keys()],
                        title="Crew Usage Distribution"
                    )
                    st.plotly_chart(fig_crew, use_container_width=True)
                
                if agent_usage:
                    # Agent usage chart
                    fig_agent = px.bar(
                        x=list(agent_usage.keys()),
                        y=list(agent_usage.values()),
                        title="Agent Utilization",
                        labels={"x": "Agent Role", "y": "Usage Count"}
                    )
                    st.plotly_chart(fig_agent, use_container_width=True)
                
                # Session metrics
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    total_messages = len(st.session_state.conversation_history)
                    st.metric("Total Messages", total_messages)
                
                with col2:
                    user_messages = sum(1 for msg in st.session_state.conversation_history if msg["role"] == "user")
                    st.metric("User Queries", user_messages)
                
                with col3:
                    crews_used = len(set(msg.get("crew_used", "unknown") for msg in st.session_state.conversation_history if msg["role"] == "assistant"))
                    st.metric("Crews Utilized", crews_used)
            
            else:
                st.info("💡 Start a conversation to see analytics!")
        
        else:
            st.error("❌ Failed to get system information")
    
    except Exception as e:
        st.error(f"Failed to load CrewAI analytics: {e}")

# Footer
st.markdown("---")
st.markdown("""
<div style="text-align: center; color: #666; padding: 1rem;">
    <p><strong>CrewAI-Enhanced AWS Cost Optimization Assistant</strong></p>
    <p>Powered by Multi-Agent AI for Specialized AWS Consulting</p>
    <p><small>Version 3.0.0 | Advanced CrewAI Integration</small></p>
</div>
""", unsafe_allow_html=True)
