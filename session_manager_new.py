"""
Enhanced Session Manager with Bedrock Integration using AWS credential chain
"""

import os
import json
import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Any, Optional
from uuid import uuid4
from botocore.exceptions import ClientError

logger = logging.getLogger(__name__)

class ConversationTurn:
    """Represents a single conversation turn"""
    
    def __init__(self, timestamp: datetime, user_message: str, assistant_response: str, 
                 tools_used: Optional[List[Dict[str, Any]]] = None, session_id: Optional[str] = None):
        self.timestamp = timestamp
        self.user_message = user_message
        self.assistant_response = assistant_response
        self.tools_used = tools_used or []
        self.session_id = session_id

    def to_dict(self) -> Dict[str, Any]:
        return {
            "timestamp": self.timestamp.isoformat(),
            "user_message": self.user_message,
            "assistant_response": self.assistant_response,
            "tools_used": self.tools_used,
            "session_id": self.session_id,
        }

class SessionConfig:
    """Configuration for session management"""
    
    def __init__(self, region_name: Optional[str] = None, profile_name: Optional[str] = None, 
                 encryption_key_arn: Optional[str] = None, session_timeout_hours: int = 2,
                 max_conversation_turns: int = 200, max_tools_per_turn: int = 25,
                 enable_cleanup: bool = True, delete_on_cleanup: bool = True,
                 recovery_max_steps: int = 200, compress_keep_recent: int = 20,
                 max_user_message_len: int = 20000, max_assistant_response_len: int = 100000,
                 max_metadata_len: int = 5000, persist_tools_minimal: bool = True):
        self.region_name = region_name or os.getenv("AWS_REGION", "ap-south-1")
        self.profile_name = profile_name or os.getenv("AWS_PROFILE", "default")
        self.encryption_key_arn = encryption_key_arn
        self.session_timeout_hours = session_timeout_hours
        self.max_conversation_turns = max_conversation_turns
        self.max_tools_per_turn = max_tools_per_turn
        self.enable_cleanup = enable_cleanup
        self.delete_on_cleanup = delete_on_cleanup
        self.recovery_max_steps = recovery_max_steps
        self.compress_keep_recent = compress_keep_recent
        self.max_user_message_len = max_user_message_len
        self.max_assistant_response_len = max_assistant_response_len
        self.max_metadata_len = max_metadata_len
        self.persist_tools_minimal = persist_tools_minimal

class BedrockSessionClient:
    """AWS Bedrock Agent Runtime client using credential chain"""
    
    def __init__(self, region_name: str, profile_name: Optional[str] = None, 
                 encryption_key_arn: Optional[str] = None, session_metadata: Optional[Dict[str, str]] = None):
        self.region_name = region_name
        self.profile_name = profile_name or os.getenv('AWS_PROFILE', 'default')
        self.encryption_key_arn = encryption_key_arn
        self.session_metadata = session_metadata or {}
        
        try:
            import boto3
            # Use AWS credential chain - no explicit keys
            session = boto3.Session(profile_name=self.profile_name, region_name=region_name)
            self.client = session.client("bedrock-agent-runtime")
            
            # Verify credentials
            sts = session.client('sts')
            identity = sts.get_caller_identity()
            logger.info(f"Bedrock Agent Runtime client initialized with profile '{self.profile_name}'")
            logger.info(f"AWS Identity: {identity.get('Arn', 'Unknown')}")
        except Exception as e:
            logger.error(f"Failed to initialize Bedrock Agent Runtime client: {e}")
            raise

    def create_session(self) -> str:
        """Create a new Bedrock session"""
        try:
            params = {"sessionMetadata": self.session_metadata}
            if self.encryption_key_arn:
                params["encryptionKeyArn"] = self.encryption_key_arn
            response = self.client.create_session(**params)
            session_id = response["sessionId"]
            logger.info(f"Created Bedrock session: {session_id}")
            return session_id
        except Exception as e:
            logger.error(f"Failed to create Bedrock session: {e}")
            raise

    def delete_session(self, session_id: str):
        """Delete a Bedrock session"""
        try:
            self.client.delete_session(sessionIdentifier=session_id)
            logger.info(f"Deleted Bedrock session: {session_id}")
        except Exception as e:
            logger.error(f"Failed to delete Bedrock session {session_id}: {e}")
            raise

    def end_session(self, session_id: str):
        """End a Bedrock session"""
        try:
            self.client.end_session(sessionIdentifier=session_id)
            logger.info(f"Ended Bedrock session: {session_id}")
        except Exception as e:
            logger.error(f"Failed to end Bedrock session {session_id}: {e}")
            raise

class ChatSession:
    """Manages individual chat session with conversation history"""
    
    def __init__(self, session_id: Optional[str] = None, bedrock_backend: Optional["BedrockSessionClient"] = None,
                 invocation_description: Optional[str] = "mcp-bot conversation", config: Optional["SessionConfig"] = None,
                 require_bedrock: bool = True):
        self.config = config or SessionConfig()
        self.client_session_id = session_id or f"bedrock_{str(uuid4())[:8]}"
        
        if require_bedrock and bedrock_backend:
            self.bedrock = bedrock_backend
            try:
                self.bedrock_session_id = self.bedrock.create_session()
                self.use_bedrock_sessions = True
                logger.info(f"✅ Created Bedrock session: {self.bedrock_session_id}")
            except Exception as e:
                logger.error(f"Failed to create Bedrock session: {e}")
                raise RuntimeError(f"Bedrock session creation failed: {e}") from e
        else:
            self.use_bedrock_sessions = False
            self.bedrock = None
            self.bedrock_session_id = None

        self.session_id = self.client_session_id
        self.created_at = datetime.now(timezone.utc)
        self.last_activity = datetime.now(timezone.utc)
        self.conversation_history: List[ConversationTurn] = []
        self.context_summary = ""
        self.total_tools_used = 0

    def get_bedrock_messages(self, max_turns: int = 10) -> List[Dict[str, Any]]:
        """Convert conversation history to Bedrock message format"""
        messages = []
        recent = self.conversation_history[-max_turns:]
        for turn in recent:
            messages.append({"role": "user", "content": [{"text": turn.user_message}]})
            messages.append({"role": "assistant", "content": [{"text": turn.assistant_response}]})
        return messages

    def get_context_for_bedrock(self) -> str:
        """Compose session context summary"""
        parts = [
            f"Session started: {self.created_at.strftime('%Y-%m-%d %H:%M')}",
            f"Total conversation turns: {len(self.conversation_history)}",
            f"Total tools executed: {self.total_tools_used}"
        ]
        if self.context_summary:
            parts.append(f"Recent context: {self.context_summary}")
        return "\n".join(parts)

    def add_turn(self, user_message: str, assistant_response: str, tools_used: Optional[List[Dict]] = None):
        """Add a conversation turn"""
        tools_used = tools_used or []
        now = datetime.now(timezone.utc)
        turn = ConversationTurn(now, user_message, assistant_response, tools_used, self.session_id)
        self.conversation_history.append(turn)
        self.last_activity = now
        self.total_tools_used += len(tools_used)
        logger.info(f"Added turn to session {self.session_id}: {len(tools_used)} tools used")

    def get_session_stats(self) -> Dict[str, Any]:
        """Get session statistics"""
        return {
            "client_session_id": self.client_session_id,
            "bedrock_session_id": self.bedrock_session_id,
            "created_at": self.created_at.isoformat(),
            "last_activity": self.last_activity.isoformat(),
            "total_turns": len(self.conversation_history),
            "total_tools_used": self.total_tools_used,
            "use_bedrock_sessions": self.use_bedrock_sessions,
            "context_summary": self.context_summary,
        }

class SessionManager:
    """Manages multiple chat sessions with automatic cleanup"""
    
    def __init__(self, config: Optional[SessionConfig] = None, 
                 default_session_metadata: Optional[Dict[str, str]] = None, require_bedrock: bool = True):
        self.config = config or SessionConfig()
        self.sessions: Dict[str, ChatSession] = {}
        self.session_timeout = timedelta(hours=self.config.session_timeout_hours)

        if require_bedrock:
            try:
                self.backend = BedrockSessionClient(
                    region_name=self.config.region_name,
                    profile_name=self.config.profile_name,
                    encryption_key_arn=self.config.encryption_key_arn,
                    session_metadata=default_session_metadata
                )
                logger.info("✅ SessionManager initialized with Bedrock Agent Runtime backend")
            except Exception as e:
                logger.error(f"❌ CRITICAL: Bedrock Agent Runtime failed: {e}")
                logger.error("Check: AWS credentials, region, and Bedrock service availability")
                raise RuntimeError(f"Bedrock-only mode but Bedrock unavailable: {e}") from e
        else:
            self.backend = None
            logger.warning("SessionManager operating in local-only mode")

    def get_session(self, session_id: str) -> Optional[ChatSession]:
        """Get a session by ID"""
        return self.sessions.get(session_id)

    def get_or_create_session(self, session_id: Optional[str] = None) -> ChatSession:
        """Get existing session or create new one"""
        if session_id and session_id in self.sessions:
            session = self.sessions[session_id]
            session.last_activity = datetime.now(timezone.utc)
            return session

        session = ChatSession(
            session_id=session_id,
            bedrock_backend=self.backend,
            config=self.config,
            require_bedrock=bool(self.backend)
        )
        self.sessions[session.session_id] = session
        return session

    def delete_session(self, session_id: str) -> bool:
        """Delete a session"""
        if session_id in self.sessions:
            session = self.sessions[session_id]
            if self.backend and session.bedrock_session_id:
                try:
                    self.backend.end_session(session.bedrock_session_id)
                    if self.config.delete_on_cleanup:
                        self.backend.delete_session(session.bedrock_session_id)
                except Exception as e:
                    logger.warning(f"Failed to cleanup Bedrock session {session.bedrock_session_id}: {e}")
            del self.sessions[session_id]
            logger.info(f"Deleted session: {session_id}")
            return True
        return False

    def cleanup_expired_sessions(self) -> int:
        """Clean up expired sessions"""
        now = datetime.now(timezone.utc)
        expired_sessions = [
            session_id for session_id, session in self.sessions.items()
            if now - session.last_activity > self.session_timeout
        ]
        
        for session_id in expired_sessions:
            self.delete_session(session_id)
        
        if expired_sessions:
            logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")
        return len(expired_sessions)

    def get_all_sessions_stats(self) -> Dict[str, Any]:
        """Get statistics for all sessions"""
        total_sessions = len(self.sessions)
        total_turns = sum(len(session.conversation_history) for session in self.sessions.values())
        total_tools = sum(session.total_tools_used for session in self.sessions.values())
        
        active_sessions = 0
        now = datetime.now(timezone.utc)
        for session in self.sessions.values():
            if now - session.last_activity < timedelta(minutes=30):
                active_sessions += 1
        
        return {
            "total_sessions": total_sessions,
            "active_sessions": active_sessions,
            "total_conversation_turns": total_turns,
            "total_tools_executed": total_tools,
            "session_timeout_hours": self.session_timeout.total_seconds() / 3600,
        }

# Global session manager instance
session_manager = SessionManager(
    config=SessionConfig(
        region_name=os.getenv("AWS_REGION", "ap-south-1"),
        profile_name=os.getenv("AWS_PROFILE", "default"),
        encryption_key_arn=None,
        session_timeout_hours=2,
        max_conversation_turns=200,
        max_tools_per_turn=25,
        enable_cleanup=True,
        delete_on_cleanup=True,
        recovery_max_steps=200,
        compress_keep_recent=20
    ),
    default_session_metadata={"app": "mcp-bot", "env": "prod"},
    require_bedrock=True
)
