"""
Enhanced MCP Manager using AWS credential chain (no hardcoded keys)
"""

import logging
import os
import json
import re
from typing import Dict, List, Any, Optional
from session_manager_new import session_manager

logger = logging.getLogger(__name__)

class EnhancedMCPMixin:
    """MCP mixin with AWS credential chain authentication"""

    model_id: str = "apac.amazon.nova-lite-v1:0"

    async def get_async_bedrock_runtime(self):
        """Get async bedrock runtime client using credential chain"""
        try:
            import aioboto3
        except ImportError:
            raise ImportError("aioboto3 is required for async Bedrock operations. Install with: pip install aioboto3")

        # Use AWS credential chain - no explicit keys
        aws_profile = os.getenv('AWS_PROFILE', 'default')
        aws_region = os.getenv('AWS_REGION', 'ap-south-1')
        
        session = aioboto3.Session(profile_name=aws_profile, region_name=aws_region)
        client = session.client("bedrock-runtime")
        return await client.__aenter__()

    async def chat_with_bedrock_with_context(
        self, message: str, session_id: str, tools_available: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Enhanced Bedrock chat with AWS credential chain"""
        try:
            chat_session = session_manager.get_or_create_session(session_id)
            historical_messages = chat_session.get_bedrock_messages(max_turns=8)
            current_messages = historical_messages + [{
                "role": "user", "content": [{"text": message}]
            }]

            system_message = self._build_specialized_system_message(chat_session, tools_available, message)
            tool_config = self._build_tool_config_for_bedrock(tools_available)
            
            result = await self._execute_contextual_conversation(
                messages=current_messages, system_message=system_message, 
                tool_config=tool_config, session_id=session_id, model_id=self.model_id
            )

            chat_session.add_turn(
                user_message=message, assistant_response=result["response"], 
                tools_used=result.get("tools_used", [])
            )

            logger.info(f"Completed specialized chat for session {session_id}: {len(result.get('tools_used', []))} tools used")
            return {
                "response": result["response"],
                "tools_used": result.get("tools_used", []),
                "session_id": session_id
            }

        except Exception as e:
            error_msg = str(e)
            logger.error(f"Error in specialized chat for session {session_id}: {error_msg}")
            
            if "ValidationException" in error_msg:
                if "model" in error_msg.lower():
                    response_text = f"Model validation error. Please check your BEDROCK_MODEL_ID in .env file. Current model: {self.model_id}"
                elif "region" in error_msg.lower():
                    response_text = f"Region validation error. Please check your AWS_REGION configuration. Current region: {os.getenv('AWS_REGION', 'not set')}"
                else:
                    response_text = f"Bedrock API validation error: {error_msg}"
            elif "RetryError" in error_msg:
                response_text = "Connection retry failed. Please check AWS credentials and region configuration."
            elif "AccessDenied" in error_msg:
                response_text = "Access denied. Please check your AWS permissions for Bedrock services."
            else:
                response_text = f"I apologize, but I encountered an error: {error_msg}"

            return {"response": response_text, "tools_used": [], "session_id": session_id, "error": True}

    def _build_specialized_system_message(self, chat_session, tools_available: Optional[List[str]] = None, user_message: str = "") -> str:
        """Build specialized system message based on query type"""
        context = ""
        if hasattr(chat_session, "get_context_for_bedrock") and callable(chat_session.get_context_for_bedrock):
            context = chat_session.get_context_for_bedrock()

        message_lower = user_message.lower()
        is_service_addition = any(keyword in message_lower for keyword in [
            "add service", "adding", "cost impact", "incremental cost", "current", "existing"
        ])
        is_architecture_design = any(keyword in message_lower for keyword in [
            "design", "architecture", "from scratch", "deploy", "solution", "complete", "build"
        ])

        base_instructions = """You are an expert AWS Cloud Solutions Architect with access to comprehensive AWS MCP servers.

**Core Capabilities:**
1. **Service Addition Cost Analysis**: Calculate incremental costs when adding new services to existing infrastructure
2. **Complete Solution Design**: Generate full architecture, diagrams, costs, and system requirements for new deployments

**Always Use Tools Iteratively:**
- Use multiple tools in sequence to gather complete information
- Continue using tools until you have comprehensive data for your analysis
- Only stop when you can provide a complete, well-supported answer

**Always Include:**
- Detailed cost breakdowns (monthly/annual projections)
- Optimization recommendations (Reserved Instances, Spot, right-sizing)
- Architecture trade-offs and alternatives
- Security and compliance considerations
- Monitoring and operational overhead costs"""

        if is_service_addition:
            specialized_instructions = """
**SERVICE ADDITION COST ANALYSIS MODE:**
For service addition requests, follow this workflow:
1. **Get Current Baseline** (use cost-explorer tools):
   - Retrieve current monthly costs by service
   - Identify existing infrastructure and usage patterns
   - Get historical cost trends
2. **Calculate Incremental Costs** (use aws-pricing tools):
   - Get pricing for the new service in the specified region
   - Calculate costs based on expected usage
   - Include data transfer and integration costs
3. **Optimization Analysis** (use billing-cost-management tools):
   - Identify right-sizing opportunities
   - Recommend Reserved Instances or Savings Plans
   - Suggest cost optimization strategies
4. **Provide Comprehensive Analysis:**
   - Incremental monthly/annual costs
   - Total cost impact (percentage increase)
   - Alternative service options comparison
   - ROI analysis and break-even timeline
   - Implementation timeline and migration costs"""

        elif is_architecture_design:
            specialized_instructions = """
**COMPLETE SOLUTION DESIGN MODE:**
For architecture design requests, follow this workflow:
1. **Architecture Design** (use aws-cdk, aws-documentation tools):
   - Design Well-Architected solution based on requirements
   - Generate infrastructure templates and best practices
   - Define security, scalability, and availability patterns
2. **Generate Diagrams** (use aws-diagram tools):
   - Create professional architecture diagrams
   - Show component relationships and data flow
   - Include network topology and security boundaries
3. **Calculate Comprehensive Costs** (use aws-pricing tools):
   - Estimate all component costs (compute, storage, networking)
   - Include operational costs (monitoring, backups, support)
   - Provide scaling cost projections
4. **Define System Requirements** (use aws-cloudwatch tools):
   - Monitoring and alerting strategy
   - Performance and availability targets
   - Disaster recovery and backup requirements
5. **Deliver Complete Package:**
   - Architecture diagrams and component details
   - Detailed cost breakdown and projections
   - Implementation roadmap and timeline
   - Operational runbook and best practices"""
        else:
            specialized_instructions = """
**GENERAL AWS CONSULTING MODE:**
- Analyze the query to determine if it's service addition or architecture design
- Use appropriate tools to provide comprehensive AWS guidance
- Focus on cost optimization and Well-Architected principles"""

        tool_hint = ""
        if tools_available:
            available_servers = set()
            for tool in tools_available:
                if "::" in tool:
                    server_name = tool.split("::")[0]
                    available_servers.add(server_name)
            if available_servers:
                tool_hint = f"\n\nAvailable AWS MCP Servers: {', '.join(sorted(available_servers))}"
                tool_hint += "\nUse these tools iteratively to gather complete information before providing your final answer."

        return base_instructions + specialized_instructions + f"\n\nSession Context:\n{context}" + tool_hint

    # ... [Additional helper methods for tool configuration, validation, and execution would be here] ...
