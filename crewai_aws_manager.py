"""
CrewAI-Enhanced AWS Cost Optimization and Architecture Design System
Multi-agent orchestration using AWS credential chain (profiles)
"""

import logging
import os
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dotenv import load_dotenv
from crewai import Agent, Crew, Process, Task, LLM
from crewai.tools import BaseTool
from session_manager_new import session_manager

load_dotenv()
logger = logging.getLogger(__name__)

# MCP-Integrated AWS Tool Classes
class MCPBaseTool(BaseTool):
    """Base class for MCP-integrated tools"""

    def __init__(self, name: str, description: str, mcp_server: str, mcp_tool: str):
        super().__init__(name=name, description=description)
        self._mcp_server = mcp_server
        self._mcp_tool = mcp_tool

    async def _call_mcp_tool(self, **kwargs):
        """Call MCP tool and return result"""
        try:
            # Import here to avoid circular imports
            from main_enhanced import mcp_manager

            if not mcp_manager:
                return f"MCP manager not available. Using fallback response for {self.name}."

            connection = mcp_manager.connections.get(self._mcp_server)
            if not connection or connection.status != "connected":
                return f"MCP server {self._mcp_server} not connected. Using fallback response."

            # Call the MCP tool
            result = await connection.session.call_tool(self._mcp_tool, kwargs)
            return str(result.content[0].text) if result.content else "No response from MCP tool"

        except Exception as e:
            logger.error(f"MCP tool call failed for {self._mcp_server}::{self._mcp_tool}: {e}")
            return f"Error calling MCP tool: {e}"

class GetCostAndUsageTool(MCPBaseTool):
    def __init__(self):
        super().__init__(
            name="get_cost_and_usage",
            description="Retrieve real AWS cost and usage data for analysis using Cost Explorer",
            mcp_server="cost-explorer",
            mcp_tool="get_cost_and_usage"
        )

    def _run(self, query: str) -> str:
        try:
            import asyncio
            return asyncio.run(self._call_mcp_tool(query=query))
        except Exception as e:
            return f"Cost analysis for query: {query}. [FALLBACK] Current monthly spend: $1,234. Top services: EC2 (45%), RDS (25%), S3 (15%). Error: {e}"

class GetAWSPricingTool(MCPBaseTool):
    def __init__(self):
        super().__init__(
            name="get_aws_pricing",
            description="Get real-time AWS service pricing information",
            mcp_server="aws-pricing",
            mcp_tool="get_pricing"
        )

    def _run(self, service: str) -> str:
        try:
            import asyncio
            return asyncio.run(self._call_mcp_tool(service=service))
        except Exception as e:
            return f"Pricing information for {service}: [FALLBACK] EC2 t3.medium: $0.0416/hour, RDS db.t3.micro: $0.017/hour. Error: {e}"

class AnalyzeCostTrendsTool(MCPBaseTool):
    def __init__(self):
        super().__init__(
            name="analyze_cost_trends",
            description="Analyze real historical cost trends and patterns from AWS Cost Explorer",
            mcp_server="cost-explorer",
            mcp_tool="get_cost_trends"
        )

    def _run(self, period: str) -> str:
        try:
            import asyncio
            return asyncio.run(self._call_mcp_tool(period=period))
        except Exception as e:
            return f"Cost trend analysis for {period}: [FALLBACK] 15% increase in compute costs, 8% decrease in storage costs. Error: {e}"

class GenerateCloudFormationTemplateTool(MCPBaseTool):
    def __init__(self):
        super().__init__(
            name="generate_cloudformation_template",
            description="Generate real CloudFormation templates for AWS resources",
            mcp_server="cloudformation",
            mcp_tool="create_template"
        )

    def _run(self, requirements: str) -> str:
        try:
            import asyncio
            return asyncio.run(self._call_mcp_tool(requirements=requirements))
        except Exception as e:
            return f"CloudFormation template generated for: {requirements}. [FALLBACK] Includes VPC, EC2, RDS, and security groups. Error: {e}"

class ValidateArchitectureTool(BaseTool):
    name: str = "validate_architecture"
    description: str = "Validate architecture against AWS Well-Architected principles"

    def _run(self, architecture: str) -> str:
        return f"Architecture validation for: {architecture}. Score: 85/100. Recommendations: Add Auto Scaling, implement backup strategy."

class EstimateSolutionCostTool(MCPBaseTool):
    def __init__(self):
        super().__init__(
            name="estimate_solution_cost",
            description="Estimate real costs for proposed AWS solution using AWS Pricing API",
            mcp_server="aws-pricing",
            mcp_tool="calculate_cost"
        )

    def _run(self, solution: str) -> str:
        try:
            import asyncio
            return asyncio.run(self._call_mcp_tool(solution=solution))
        except Exception as e:
            return f"Cost estimate for solution: {solution}. [FALLBACK] Monthly: $892, Annual: $10,704. Includes 15% optimization potential. Error: {e}"

class IdentifyCostSavingsTool(MCPBaseTool):
    def __init__(self):
        super().__init__(
            name="identify_cost_savings",
            description="Identify real cost savings opportunities using AWS Cost Explorer and Billing data",
            mcp_server="billing-cost-management",
            mcp_tool="get_savings_opportunities"
        )

    def _run(self, current_setup: str) -> str:
        try:
            import asyncio
            return asyncio.run(self._call_mcp_tool(current_setup=current_setup))
        except Exception as e:
            return f"Cost savings opportunities for: {current_setup}. [FALLBACK] Potential savings: $234/month (26%) through rightsizing and Reserved Instances. Error: {e}"

class RecommendInstanceTypesTool(BaseTool):
    name: str = "recommend_instance_types"
    description: str = "Recommend optimal EC2 instance types based on workload requirements"

    def _run(self, workload: str) -> str:
        return f"Instance recommendations for {workload}: c5.large for compute-intensive, r5.xlarge for memory-intensive workloads."

class AWSCrewManager:
    """CrewAI-based manager using AWS credential chain instead of hardcoded keys"""
    
    def __init__(self):
        self.model_id = os.getenv("BEDROCK_MODEL_ID", "apac.amazon.nova-lite-v1:0")
        self._recent_model_errors = []
        self.llm = self._initialize_bedrock_llm()
        self._aws_tools = self._initialize_aws_tools()
        self.agents = self._create_aws_agents()
        self.crews = self._create_specialized_crews()
        logger.info("CrewAI AWS Manager initialized with credential chain authentication")

    def _initialize_bedrock_llm(self) -> LLM:
        """Initialize Bedrock LLM using AWS credential chain (no explicit keys)"""
        try:
            aws_region = os.getenv('AWS_REGION', 'ap-south-1')
            aws_profile = os.getenv('AWS_PROFILE', 'default')
            
            logger.info(f"Initializing Bedrock LLM with model: {self.model_id}")
            logger.info(f"AWS Region: {aws_region}")
            logger.info(f"AWS Profile: {aws_profile}")
            
            try:
                import boto3
                # Use AWS credential chain - no explicit keys needed
                session = boto3.Session(profile_name=aws_profile, region_name=aws_region)
                
                # Test credentials
                sts = session.client('sts')
                identity = sts.get_caller_identity()
                logger.info(f"✅ AWS Identity verified: {identity.get('Arn', 'Unknown')}")
                
                # Initialize CrewAI LLM with profile-based auth
                return LLM(
                    model=f"bedrock/{self.model_id}",
                    aws_region_name=aws_region,
                    temperature=0.1,
                    max_tokens=4000
                )
                
            except Exception as e:
                logger.warning(f"AWS credential chain failed: {e}")
                logger.warning("Falling back to OpenAI model")
                os.environ["OPENAI_API_KEY"] = "dummy-key-for-testing"
                return LLM(model="gpt-4")
                
        except Exception as e:
            logger.error(f"Failed to initialize Bedrock LLM: {e}")
            logger.warning("Falling back to OpenAI model")
            os.environ["OPENAI_API_KEY"] = "dummy-key-for-testing"
            return LLM(model="gpt-4")

    def _initialize_aws_tools(self) -> List[BaseTool]:
        """Initialize AWS tools for CrewAI agents"""
        return [
            GetCostAndUsageTool(), GetAWSPricingTool(), AnalyzeCostTrendsTool(),
            GenerateCloudFormationTemplateTool(), ValidateArchitectureTool(), 
            EstimateSolutionCostTool(), IdentifyCostSavingsTool(), RecommendInstanceTypesTool()
        ]

    def _create_aws_agents(self) -> Dict[str, Agent]:
        """Create specialized AWS agents"""
        agents = {
            'cost_analyst': Agent(
                role='Senior AWS Cost Analyst',
                goal='Analyze AWS costs, identify optimization opportunities, and provide detailed financial insights',
                backstory="""You are a seasoned AWS cost optimization expert with deep knowledge of AWS billing and cost structures, Reserved Instances and Savings Plans, cost allocation and tagging strategies, FinOps best practices, and multi-account cost management. You excel at finding hidden costs and providing actionable optimization recommendations.""",
                llm=self.llm, tools=self._get_tools_for_agent('cost_analyst'),
                verbose=True, memory=True, max_iter=5, allow_delegation=True
            ),
            'solutions_architect': Agent(
                role='AWS Solutions Architect',
                goal='Design optimal, scalable, and cost-effective AWS architectures',
                backstory="""You are an expert AWS Solutions Architect with extensive experience in AWS Well-Architected Framework, multi-tier application design, microservices and serverless architectures, security and compliance requirements, and high availability and disaster recovery. You create comprehensive solutions that balance performance, cost, and scalability.""",
                llm=self.llm, tools=self._get_tools_for_agent('solutions_architect'),
                verbose=True, memory=True, max_iter=5, allow_delegation=True
            ),
            'optimization_specialist': Agent(
                role='AWS Optimization Specialist',
                goal='Provide specific optimization recommendations and implementation guidance',
                backstory="""You are a hands-on AWS optimization specialist focused on right-sizing recommendations, performance optimization, cost-performance trade-offs, automation and monitoring setup, and implementation best practices. You provide practical, actionable advice for immediate improvements.""",
                llm=self.llm, tools=self._get_tools_for_agent('optimization_specialist'),
                verbose=True, memory=True, max_iter=3, allow_delegation=False
            ),
            'compliance_advisor': Agent(
                role='AWS Compliance and Security Advisor',
                goal='Ensure solutions meet security and compliance requirements while optimizing costs',
                backstory="""You are a security-focused AWS expert specializing in AWS security best practices, compliance frameworks (SOC 2, PCI DSS, HIPAA, GDPR), cost-effective security implementations, risk assessment and mitigation, and governance and policy enforcement. You ensure solutions are both secure and cost-optimized.""",
                llm=self.llm, tools=self._get_tools_for_agent('compliance_advisor'),
                verbose=True, memory=True, max_iter=3, allow_delegation=False
            )
        }
        return agents

    def _get_tools_for_agent(self, agent_type: str) -> List[BaseTool]:
        """Get specific tools for each agent type"""
        tool_mapping = {
            'cost_analyst': ['get_cost_and_usage', 'get_aws_pricing', 'analyze_cost_trends', 'identify_cost_savings'],
            'solutions_architect': ['generate_cloudformation_template', 'validate_architecture', 'estimate_solution_cost'],
            'optimization_specialist': ['recommend_instance_types', 'identify_cost_savings', 'get_aws_pricing'],
            'compliance_advisor': ['validate_architecture', 'estimate_solution_cost']
        }
        agent_tool_names = tool_mapping.get(agent_type, [])
        return [tool for tool in self._aws_tools if tool.name in agent_tool_names]

    def _create_specialized_crews(self) -> Dict[str, Crew]:
        """Create specialized crews for different AWS consulting scenarios"""
        return {
            'cost_analysis': Crew(
                agents=[self.agents['cost_analyst'], self.agents['optimization_specialist']],
                tasks=[], process=Process.sequential, memory=True, verbose=True, max_rpm=10
            ),
            'architecture_design': Crew(
                agents=[self.agents['solutions_architect'], self.agents['cost_analyst'], self.agents['compliance_advisor']],
                tasks=[], process=Process.hierarchical, manager_llm=self.llm, memory=True, verbose=True, max_rpm=10
            ),
            'optimization_review': Crew(
                agents=[self.agents['optimization_specialist'], self.agents['cost_analyst']],
                tasks=[], process=Process.sequential, memory=True, verbose=True, max_rpm=10
            ),
            'comprehensive_analysis': Crew(
                agents=list(self.agents.values()), tasks=[], process=Process.hierarchical,
                manager_llm=self.llm, memory=True, verbose=True, max_rpm=8
            )
        }

    async def chat_with_crewai_context(self, message: str, session_id: str, crew_type: Optional[str] = None) -> Dict[str, Any]:
        """Enhanced chat using CrewAI multi-agent system"""
        try:
            chat_session = session_manager.get_or_create_session(session_id)
            
            if not crew_type:
                crew_type = self._detect_crew_type(message)
            
            tasks = self._create_tasks_for_query(message, crew_type)
            crew = self.crews[crew_type]
            crew.tasks = tasks
            
            if len(self._recent_model_errors) >= 3:
                logger.warning("Circuit breaker activated: using simplified mode")
                result = await self._execute_single_agent_fallback(message, crew_type)
            else:
                result = await self._execute_crew_with_recovery(crew, message, session_id)
            
            chat_session.add_turn(
                user_message=message,
                assistant_response=result["response"],
                tools_used=result.get("tools_used", [])
            )
            
            return {
                "response": result["response"],
                "tools_used": result.get("tools_used", []),
                "crew_used": crew_type,
                "agents_involved": [agent.role for agent in crew.agents],
                "session_id": session_id
            }
            
        except Exception as e:
            error_msg = str(e)
            logger.error(f"CrewAI execution error for session {session_id}: {error_msg}")
            
            if "ModelErrorException" in error_msg or "API Error" in error_msg:
                self._recent_model_errors.append(datetime.now())
                self._recent_model_errors = [
                    error_time for error_time in self._recent_model_errors
                    if error_time > datetime.now() - timedelta(minutes=10)
                ]
            
            return {
                "response": f"I encountered an issue but can provide AWS guidance based on my knowledge. {self._get_fallback_response(message)}",
                "tools_used": [], "crew_used": "fallback", "session_id": session_id, "error": True
            }

    def _detect_crew_type(self, message: str) -> str:
        """Detect which crew should handle the query"""
        message_lower = message.lower()
        if any(keyword in message_lower for keyword in ["cost", "pricing", "bill", "expense", "budget", "saving", "optimize cost"]):
            return "cost_analysis"
        elif any(keyword in message_lower for keyword in ["design", "architecture", "solution", "deploy", "build", "create"]):
            return "architecture_design"
        elif any(keyword in message_lower for keyword in ["optimize", "improve", "performance", "right-size", "efficiency"]):
            return "optimization_review"
        else:
            return "comprehensive_analysis"

    def _create_tasks_for_query(self, message: str, crew_type: str) -> List[Task]:
        """Create dynamic tasks based on query and crew type"""
        tasks = []
        if crew_type == "cost_analysis":
            tasks = [
                Task(description=f"Analyze the following AWS cost question: {message}. Provide detailed cost breakdown and analysis.",
                     expected_output="Comprehensive cost analysis with specific recommendations and pricing details",
                     agent=self.agents['cost_analyst']),
                Task(description="Based on the cost analysis, provide specific optimization recommendations with implementation steps.",
                     expected_output="Actionable optimization recommendations with expected savings and implementation timeline",
                     agent=self.agents['optimization_specialist'])
            ]
        elif crew_type == "architecture_design":
            tasks = [
                Task(description=f"Design an AWS solution for: {message}. Create a comprehensive architecture following AWS Well-Architected principles.",
                     expected_output="Complete architecture design with component details, data flow, and scalability considerations",
                     agent=self.agents['solutions_architect']),
                Task(description="Calculate detailed cost estimates for the proposed architecture including setup and operational costs.",
                     expected_output="Detailed cost breakdown with monthly/annual projections and cost optimization opportunities",
                     agent=self.agents['cost_analyst']),
                Task(description="Review the architecture for security and compliance requirements, ensuring cost-effective implementation.",
                     expected_output="Security and compliance assessment with recommendations for cost-effective implementation",
                     agent=self.agents['compliance_advisor'])
            ]
        elif crew_type == "optimization_review":
            tasks = [
                Task(description=f"Review and optimize the following AWS setup: {message}. Identify specific areas for improvement.",
                     expected_output="Detailed optimization report with specific recommendations and expected impact",
                     agent=self.agents['optimization_specialist']),
                Task(description="Quantify the potential cost savings and provide implementation roadmap for the optimization recommendations.",
                     expected_output="Cost savings analysis with prioritized implementation plan and ROI calculations",
                     agent=self.agents['cost_analyst'])
            ]
        else:
            tasks = [
                Task(description=f"Provide comprehensive AWS consulting for: {message}. Analyze all aspects including cost, architecture, and optimization.",
                     expected_output="Complete AWS consulting report covering all relevant aspects with actionable recommendations",
                     agent=self.agents['solutions_architect'])
            ]
        return tasks

    async def _execute_crew_with_recovery(self, crew: Crew, message: str, session_id: str) -> Dict[str, Any]:
        """Execute crew with error recovery mechanisms"""
        try:
            result = crew.kickoff()
            tools_used = []
            for agent in crew.agents:
                if hasattr(agent, '_tools_used'):
                    tools_used.extend(agent._tools_used)
            return {"response": str(result), "tools_used": tools_used, "success": True}
        except Exception as e:
            logger.error(f"Crew execution failed: {e}")
            return await self._execute_single_agent_fallback(message, "cost_analysis")

    async def _execute_single_agent_fallback(self, message: str, crew_type: str) -> Dict[str, Any]:
        """Fallback to single agent execution when crew fails"""
        try:
            agent_mapping = {
                "cost_analysis": self.agents['cost_analyst'],
                "architecture_design": self.agents['solutions_architect'],
                "optimization_review": self.agents['optimization_specialist'],
                "comprehensive_analysis": self.agents['solutions_architect']
            }
            agent = agent_mapping.get(crew_type, self.agents['cost_analyst'])
            task = Task(description=f"Provide AWS consulting for: {message}",
                       expected_output="Comprehensive response with actionable recommendations", agent=agent)
            single_crew = Crew(agents=[agent], tasks=[task], process=Process.sequential, verbose=False)
            result = single_crew.kickoff()
            return {"response": str(result), "tools_used": [], "success": True, "fallback_mode": True}
        except Exception as e:
            logger.error(f"Single agent fallback failed: {e}")
            return {"response": self._get_fallback_response(message), "tools_used": [], "success": False, "fallback_mode": True}

    def _get_fallback_response(self, message: str) -> str:
        """Generate fallback response when all agents fail"""
        if "cost" in message.lower():
            return "For cost analysis, I recommend using AWS Cost Explorer to review your spending patterns and identify optimization opportunities. Consider Reserved Instances for predictable workloads and Spot Instances for flexible computing needs."
        elif "architecture" in message.lower():
            return "For architecture design, follow AWS Well-Architected principles: operational excellence, security, reliability, performance efficiency, and cost optimization. Start with the AWS Architecture Center for reference architectures."
        else:
            return "I recommend reviewing your AWS setup using the AWS Trusted Advisor and Cost Explorer tools. Focus on right-sizing instances, optimizing storage, and implementing cost monitoring practices."

# Initialize global CrewAI manager (will be initialized on first import)
crewai_manager = None

def get_crewai_manager():
    """Get or create the global CrewAI manager instance"""
    global crewai_manager
    if crewai_manager is None:
        crewai_manager = AWSCrewManager()
    return crewai_manager
